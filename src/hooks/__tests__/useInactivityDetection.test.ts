/**
 * Tests for useInactivityDetection hook
 * 
 * Tests inactivity monitoring, warning system, and automatic timer pausing.
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useInactivityDetection } from '../useInactivityDetection';
import { DEFAULT_INACTIVITY_SETTINGS } from '../../types/timer';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock useLocalStorage hook
vi.mock('../useLocalStorage', () => ({
  useLocalStorage: (key: string, defaultValue: any) => {
    const [value, setValue] = React.useState(defaultValue);
    return [value, setValue];
  },
}));

// Mock React
import React from 'react';
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useState: vi.fn(),
    useEffect: vi.fn(),
    useRef: vi.fn(),
    useCallback: vi.fn(),
  };
});

describe('useInactivityDetection', () => {
  let mockSetState: ReturnType<typeof vi.fn>;
  let mockUseEffect: ReturnType<typeof vi.fn>;
  let mockUseRef: ReturnType<typeof vi.fn>;
  let mockUseCallback: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock React hooks
    mockSetState = vi.fn();
    mockUseEffect = vi.fn();
    mockUseRef = vi.fn(() => ({ current: null }));
    mockUseCallback = vi.fn((fn) => fn);

    (React.useState as any).mockImplementation((initial: any) => [initial, mockSetState]);
    (React.useEffect as any).mockImplementation(mockUseEffect);
    (React.useRef as any).mockImplementation(mockUseRef);
    (React.useCallback as any).mockImplementation(mockUseCallback);

    // Mock DOM methods
    vi.spyOn(document, 'addEventListener');
    vi.spyOn(document, 'removeEventListener');
    vi.spyOn(window, 'dispatchEvent');
    
    // Mock timers
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  describe('initialization', () => {
    it('should initialize with default settings', () => {
      const { result } = renderHook(() => useInactivityDetection());

      expect(result.current.settings).toEqual(DEFAULT_INACTIVITY_SETTINGS);
      expect(result.current.isActive).toBe(true);
      expect(result.current.isWarningShown).toBe(false);
      expect(result.current.warningTimeRemaining).toBe(0);
    });

    it('should set up activity event listeners when enabled', () => {
      renderHook(() => useInactivityDetection());

      // Check that useEffect was called (would set up listeners)
      expect(mockUseEffect).toHaveBeenCalled();
    });
  });

  describe('activity detection', () => {
    it('should update activity state on user activity', () => {
      const { result } = renderHook(() => useInactivityDetection());

      act(() => {
        result.current.resetActivity();
      });

      // Should update activity state
      expect(mockUseCallback).toHaveBeenCalled();
    });

    it('should clear warning when activity is detected', () => {
      const { result } = renderHook(() => useInactivityDetection());

      // Simulate warning state
      act(() => {
        // This would normally be triggered by inactivity
        result.current.resetActivity();
      });

      // Warning should be cleared
      expect(result.current.isWarningShown).toBe(false);
    });
  });

  describe('inactivity detection', () => {
    it('should detect inactivity after threshold', async () => {
      const settings = {
        ...DEFAULT_INACTIVITY_SETTINGS,
        thresholdMinutes: 1, // 1 minute threshold
        showWarningBeforePause: false,
      };

      const { result } = renderHook(() => useInactivityDetection());

      // Mock settings
      result.current.settings = settings;

      // Fast-forward time past threshold
      act(() => {
        vi.advanceTimersByTime(61000); // 61 seconds
      });

      // Should trigger inactivity detection
      await waitFor(() => {
        expect(window.dispatchEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'timer-pause-inactivity',
          })
        );
      });
    });

    it('should show warning before auto-pause when enabled', async () => {
      const settings = {
        ...DEFAULT_INACTIVITY_SETTINGS,
        thresholdMinutes: 1,
        showWarningBeforePause: true,
        warningDurationSeconds: 30,
      };

      const { result } = renderHook(() => useInactivityDetection());
      result.current.settings = settings;

      // Fast-forward to trigger warning
      act(() => {
        vi.advanceTimersByTime(61000); // Past threshold
      });

      await waitFor(() => {
        expect(result.current.isWarningShown).toBe(true);
        expect(result.current.warningTimeRemaining).toBe(30);
      });
    });

    it('should auto-pause after warning period expires', async () => {
      const settings = {
        ...DEFAULT_INACTIVITY_SETTINGS,
        thresholdMinutes: 1,
        showWarningBeforePause: true,
        warningDurationSeconds: 10,
      };

      const { result } = renderHook(() => useInactivityDetection());
      result.current.settings = settings;

      // Trigger warning
      act(() => {
        vi.advanceTimersByTime(61000);
      });

      // Wait for warning period to expire
      act(() => {
        vi.advanceTimersByTime(11000); // 11 seconds
      });

      await waitFor(() => {
        expect(window.dispatchEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'timer-pause-inactivity',
          })
        );
      });
    });
  });

  describe('settings management', () => {
    it('should update inactivity settings', async () => {
      const { result } = renderHook(() => useInactivityDetection());

      const newSettings = {
        ...DEFAULT_INACTIVITY_SETTINGS,
        thresholdMinutes: 30,
      };

      await act(async () => {
        await result.current.updateSettings(newSettings);
      });

      expect(result.current.settings).toEqual(
        expect.objectContaining(newSettings)
      );
    });

    it('should disable detection when settings disabled', () => {
      const { result } = renderHook(() => useInactivityDetection());

      act(() => {
        result.current.updateSettings({ enabled: false });
      });

      expect(result.current.isActive).toBe(false);
    });
  });

  describe('detection control', () => {
    it('should pause detection', () => {
      const { result } = renderHook(() => useInactivityDetection());

      act(() => {
        result.current.pauseDetection();
      });

      expect(result.current.isActive).toBe(false);
    });

    it('should resume detection', () => {
      const { result } = renderHook(() => useInactivityDetection());

      // First pause
      act(() => {
        result.current.pauseDetection();
      });

      // Then resume
      act(() => {
        result.current.resumeDetection();
      });

      expect(result.current.isActive).toBe(true);
    });
  });

  describe('warning countdown', () => {
    it('should countdown warning time', async () => {
      const { result } = renderHook(() => useInactivityDetection());

      // Mock warning state
      result.current.isWarningShown = true;
      result.current.warningTimeRemaining = 10;

      // Advance timer by 1 second
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      // Should decrement warning time
      await waitFor(() => {
        expect(result.current.warningTimeRemaining).toBeLessThan(10);
      });
    });

    it('should auto-pause when countdown reaches zero', async () => {
      const { result } = renderHook(() => useInactivityDetection());

      // Set warning with 1 second remaining
      result.current.isWarningShown = true;
      result.current.warningTimeRemaining = 1;

      // Advance timer past countdown
      act(() => {
        vi.advanceTimersByTime(1100);
      });

      await waitFor(() => {
        expect(window.dispatchEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'timer-pause-inactivity',
          })
        );
      });
    });
  });

  describe('cleanup', () => {
    it('should clean up event listeners on unmount', () => {
      const { unmount } = renderHook(() => useInactivityDetection());

      unmount();

      // Should have called cleanup function in useEffect
      expect(mockUseEffect).toHaveBeenCalled();
    });

    it('should clear intervals on unmount', () => {
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      const { unmount } = renderHook(() => useInactivityDetection());

      unmount();

      // Cleanup should clear intervals
      expect(clearIntervalSpy).toHaveBeenCalled();
    });
  });

  describe('edge cases', () => {
    it('should handle rapid activity changes', () => {
      const { result } = renderHook(() => useInactivityDetection());

      // Rapid activity updates
      act(() => {
        result.current.resetActivity();
        result.current.resetActivity();
        result.current.resetActivity();
      });

      // Should handle gracefully without errors
      expect(result.current.isActive).toBe(true);
    });

    it('should handle settings changes during warning', async () => {
      const { result } = renderHook(() => useInactivityDetection());

      // Start warning
      result.current.isWarningShown = true;
      result.current.warningTimeRemaining = 10;

      // Change settings during warning
      await act(async () => {
        await result.current.updateSettings({
          enabled: false,
        });
      });

      // Should handle gracefully
      expect(result.current.settings.enabled).toBe(false);
    });

    it('should handle zero threshold gracefully', async () => {
      const { result } = renderHook(() => useInactivityDetection());

      await act(async () => {
        await result.current.updateSettings({
          thresholdMinutes: 0,
        });
      });

      // Should not crash with zero threshold
      expect(result.current.settings.thresholdMinutes).toBe(0);
    });
  });
});
