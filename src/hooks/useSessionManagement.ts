/**
 * Session Management Hook
 * 
 * Manages task sessions and timer instances with proper state management,
 * error handling, and integration with the backend.
 */

import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  TaskSession, 
  TimerInstance, 
  UseSessionManagementReturn,
  SessionStats 
} from '../types/timer';
import { useNotification } from '../contexts/NotificationContext';
import { measurePerformance } from '../utils/performance';

export function useSessionManagement(): UseSessionManagementReturn {
  const [sessions, setSessions] = useState<TaskSession[]>([]);
  const [activeSession, setActiveSessionState] = useState<TaskSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { showError, showSuccess } = useNotification();

  // Load sessions from backend
  const loadSessions = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const loadedSessions = await invoke<TaskSession[]>('get_sessions');
      setSessions(loadedSessions);
      
      // Find active session
      const active = loadedSessions.find(s => s.isActive) || null;
      setActiveSessionState(active);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load sessions';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [showError]);

  // Initialize sessions on mount
  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  const createSession = useCallback(async (taskId: string, taskName: string): Promise<TaskSession> => {
    return measurePerformance('useSessionManagement.createSession', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const newSession = await invoke<TaskSession>('create_session', {
          taskId,
          taskName: taskName.trim()
        });
        
        setSessions(prev => [...prev, newSession]);
        showSuccess(`Session created for "${taskName}"`);
        
        return newSession;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to create session';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [showError, showSuccess]);

  const updateSession = useCallback(async (sessionId: string, updates: Partial<TaskSession>): Promise<TaskSession> => {
    return measurePerformance('useSessionManagement.updateSession', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const updatedSession = await invoke<TaskSession>('update_session', {
          sessionId,
          updates
        });
        
        setSessions(prev => prev.map(s => s.id === sessionId ? updatedSession : s));
        
        // Update active session if it was the one being updated
        if (activeSession?.id === sessionId) {
          setActiveSessionState(updatedSession);
        }
        
        return updatedSession;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update session';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [activeSession, showError]);

  const deleteSession = useCallback(async (sessionId: string): Promise<void> => {
    return measurePerformance('useSessionManagement.deleteSession', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        await invoke('delete_session', { sessionId });
        
        setSessions(prev => prev.filter(s => s.id !== sessionId));
        
        // Clear active session if it was deleted
        if (activeSession?.id === sessionId) {
          setActiveSessionState(null);
        }
        
        showSuccess('Session deleted successfully');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to delete session';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [activeSession, showError, showSuccess]);

  const getSessionById = useCallback((sessionId: string): TaskSession | undefined => {
    return sessions.find(s => s.id === sessionId);
  }, [sessions]);

  const getSessionsByTaskId = useCallback((taskId: string): TaskSession[] => {
    return sessions.filter(s => s.taskId === taskId);
  }, [sessions]);

  const getActiveSession = useCallback((): TaskSession | null => {
    return activeSession;
  }, [activeSession]);

  const setActiveSession = useCallback(async (session: TaskSession | null): Promise<void> => {
    return measurePerformance('useSessionManagement.setActiveSession', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Update all sessions to set the active one
        const updatedSessions = sessions.map(s => ({
          ...s,
          isActive: s.id === session?.id
        }));
        
        // Update each session in the backend
        for (const updatedSession of updatedSessions) {
          if (updatedSession.isActive !== sessions.find(s => s.id === updatedSession.id)?.isActive) {
            await invoke('update_session', {
              sessionId: updatedSession.id,
              updates: { isActive: updatedSession.isActive }
            });
          }
        }
        
        setSessions(updatedSessions);
        setActiveSessionState(session);
        
        if (session) {
          showSuccess(`Activated session for "${session.taskName}"`);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to set active session';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [sessions, showError, showSuccess]);

  // Create timer instance within a session
  const createTimerInstance = useCallback(async (sessionId: string): Promise<TimerInstance> => {
    return measurePerformance('useSessionManagement.createTimerInstance', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const newInstance = await invoke<TimerInstance>('create_timer_instance', {
          sessionId
        });
        
        // Reload sessions to get updated data
        await loadSessions();
        
        showSuccess('New timer instance created');
        return newInstance;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to create timer instance';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [loadSessions, showError, showSuccess]);

  // Start a timer instance
  const startTimerInstance = useCallback(async (instanceId: string): Promise<void> => {
    return measurePerformance('useSessionManagement.startTimerInstance', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        await invoke('start_timer_instance', { instanceId });
        
        // Reload sessions to get updated data
        await loadSessions();
        
        showSuccess('Timer started');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to start timer';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [loadSessions, showError, showSuccess]);

  // Stop a timer instance
  const stopTimerInstance = useCallback(async (instanceId: string): Promise<void> => {
    return measurePerformance('useSessionManagement.stopTimerInstance', async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        await invoke('stop_timer_instance', { instanceId });
        
        // Reload sessions to get updated data
        await loadSessions();
        
        showSuccess('Timer stopped');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to stop timer';
        setError(errorMessage);
        showError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    });
  }, [loadSessions, showError, showSuccess]);

  return {
    sessions,
    activeSession,
    createSession,
    updateSession,
    deleteSession,
    getSessionById,
    getSessionsByTaskId,
    getActiveSession,
    setActiveSession,
    // Timer instance methods
    createTimerInstance,
    startTimerInstance,
    stopTimerInstance,
    isLoading,
    error,
  };
}
