{"name": "taskmint", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest run", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --watchAll=false", "test:unit": "vitest run --exclude src/__tests__/integration/", "test:integration": "vitest run src/__tests__/integration/", "test:all": "vitest run --coverage --verbose"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.6.0", "@sentry/react": "^8.42.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-notification": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-store": "^2", "@types/react-window": "^1.8.8", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "moment": "^2.30.1", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-dom": "^18.3.1", "react-window": "^1.8.11", "recharts": "^2.15.4", "zod": "^3.25.39"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.1", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "rollup-plugin-visualizer": "^6.0.3", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^3.2.4"}}